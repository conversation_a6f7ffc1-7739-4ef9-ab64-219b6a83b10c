#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS Power Map Visualization
Combines GPS coordinates and power values from spectrum data and displays them on a map
"""

import requests
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO
import numpy as np
from typing import List, Tuple
import os
import sys

# Import functions from existing modules
from swp_mode_playback import extract_gps_and_power_for_map
from map.WGS84_GCJ02 import wgs84_to_gcj02


def get_power_color(power_dbm: float) -> str:
    """
    Convert power value to color for visualization
    
    Args:
        power_dbm: Power value in dBm
        
    Returns:
        Color string in hex format
    """
    # Normalize power to 0-1 range (assuming -100 to -50 dBm range)
    min_power = -100.0
    max_power = -50.0
    
    # Clamp the power value
    normalized_power = max(0, min(1, (power_dbm - min_power) / (max_power - min_power)))
    
    # Color gradient from blue (low power) to red (high power)
    if normalized_power < 0.5:
        # Blue to green
        r = 0
        g = int(255 * normalized_power * 2)
        b = int(255 * (1 - normalized_power * 2))
    else:
        # Green to red
        r = int(255 * (normalized_power - 0.5) * 2)
        g = int(255 * (1 - (normalized_power - 0.5) * 2))
        b = 0
    
    return f"#{r:02x}{g:02x}{b:02x}"


def create_map_with_gps_power(gps_power_data: List[Tuple[float, float, float]], 
                              api_key: str = "fd2adfc70d86343b1181195c0fc9a0ee") -> bool:
    """
    Create a map with GPS coordinates and power values
    
    Args:
        gps_power_data: List of (latitude, longitude, power_dBm) tuples
        api_key: Amap API key
        
    Returns:
        True if successful, False otherwise
    """
    if not gps_power_data:
        print("No GPS power data available")
        return False
    
    # Calculate center coordinates
    latitudes = [data[0] for data in gps_power_data]
    longitudes = [data[1] for data in gps_power_data]
    powers = [data[2] for data in gps_power_data]
    
    center_lat = sum(latitudes) / len(latitudes)
    center_lon = sum(longitudes) / len(longitudes)
    
    print(f"Center coordinates: ({center_lat:.6f}, {center_lon:.6f})")
    print(f"Power range: {min(powers):.2f} to {max(powers):.2f} dBm")
    
    # Convert to GCJ-02 coordinate system for Amap
    center_lon_gcj, center_lat_gcj = wgs84_to_gcj02(center_lon, center_lat)
    
    # Map parameters
    width = 1920
    height = 1080
    zoom = 16
    
    # Build markers string for all GPS points
    # Use simpler marker format for better compatibility
    markers_list = []
    for i, (lat, lon, power) in enumerate(gps_power_data):
        # Convert each point to GCJ-02
        lon_gcj, lat_gcj = wgs84_to_gcj02(lon, lat)

        # Use red color for all markers to simplify (high power = red, low power = blue)
        if power > -80:  # High power
            color_code = "0xFF0000"  # Red
        else:  # Low power
            color_code = "0x0000FF"  # Blue

        # Add marker (limit to first 10 points to avoid URL length issues)
        if i < 10:
            markers_list.append(f"mid,{color_code},A:{lon_gcj},{lat_gcj}")

    # If no markers, add at least one at center
    if not markers_list:
        markers_list.append(f"mid,0xFF0000,A:{center_lon_gcj},{center_lat_gcj}")

    markers_str = "|".join(markers_list)
    
    # Amap static map API URL
    url = "https://restapi.amap.com/v3/staticmap"
    
    # Request parameters
    params = {
        "location": f"{center_lon_gcj},{center_lat_gcj}",
        "zoom": zoom,
        "size": f"{width}*{height}",
        "scale": 2,
        "maptype": "satellite",
        "markers": markers_str,
        "key": api_key
    }
    
    try:
        # Debug: Print request details
        print("Requesting map from Amap API...")
        print(f"API URL: {url}")
        print(f"Center coordinates (GCJ02): {center_lon_gcj:.6f}, {center_lat_gcj:.6f}")
        print(f"Number of markers: {len(markers_list)}")
        print(f"Request parameters:")
        for key, value in params.items():
            if key == "markers":
                print(f"  {key}: {value[:100]}..." if len(str(value)) > 100 else f"  {key}: {value}")
            else:
                print(f"  {key}: {value}")

        # Send request
        response = requests.get(url, params=params, timeout=30)

        print(f"Response status code: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")

        if response.status_code == 200:
            # Check if response is actually an image
            content_type = response.headers.get('content-type', '')
            print(f"Content type: {content_type}")
            print(f"Response size: {len(response.content)} bytes")

            # Save the map image
            map_filename = "gps_power_map.png"
            with open(map_filename, "wb") as f:
                f.write(response.content)

            print(f"Map saved as: {map_filename}")

            # Check if it's actually an image or error message
            if content_type.startswith('image/'):
                print("✅ Successfully received image from Amap API")
                # Create enhanced visualization with matplotlib
                create_enhanced_visualization(gps_power_data, map_filename)
                return True
            else:
                print("❌ Response is not an image, likely an error message:")
                try:
                    print(response.text[:500])  # Print first 500 chars of response
                except:
                    print("Could not decode response text")
                # Still create visualization without map background
                create_enhanced_visualization(gps_power_data, None)
                return False
        else:
            print(f"❌ Failed to fetch map: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            # Create visualization without map background
            create_enhanced_visualization(gps_power_data, None)
            return False

    except Exception as e:
        print(f"❌ Error creating map: {e}")
        import traceback
        traceback.print_exc()
        # Create visualization without map background
        create_enhanced_visualization(gps_power_data, None)
        return False


def create_enhanced_visualization(gps_power_data: List[Tuple[float, float, float]],
                                map_filename: str = None):
    """
    Create enhanced visualization with power values and legend

    Args:
        gps_power_data: List of (latitude, longitude, power_dBm) tuples
        map_filename: Background map image filename (optional)
    """
    # Create figure with subplots
    if map_filename and os.path.exists(map_filename):
        # If we have a map background, create 3 subplots
        fig = plt.figure(figsize=(20, 8))
        ax1 = plt.subplot(1, 3, 1)
        ax2 = plt.subplot(1, 3, 2)
        ax3 = plt.subplot(1, 3, 3)

        # Display the map image
        try:
            map_img = Image.open(map_filename)
            ax3.imshow(map_img)
            ax3.set_title('Satellite Map with GPS Markers')
            ax3.axis('off')
        except Exception as e:
            print(f"Could not display map image: {e}")
            ax3.text(0.5, 0.5, 'Map image\nnot available',
                    ha='center', va='center', transform=ax3.transAxes)
            ax3.set_title('Map Display Error')
    else:
        # Without map background, create 2 subplots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # Extract data
    latitudes = [data[0] for data in gps_power_data]
    longitudes = [data[1] for data in gps_power_data]
    powers = [data[2] for data in gps_power_data]
    
    # Plot 1: GPS coordinates with power color coding
    scatter = ax1.scatter(longitudes, latitudes, c=powers, cmap='coolwarm', 
                         s=100, alpha=0.7, edgecolors='black', linewidth=1)
    ax1.set_xlabel('Longitude')
    ax1.set_ylabel('Latitude')
    ax1.set_title('GPS Coordinates with Power Values')
    ax1.grid(True, alpha=0.3)
    
    # Add colorbar
    cbar = plt.colorbar(scatter, ax=ax1)
    cbar.set_label('Power (dBm)')
    
    # Add point labels
    for i, (lat, lon, power) in enumerate(gps_power_data[:10]):  # Label first 10 points
        ax1.annotate(f'P{i}\n{power:.1f}dBm', 
                    (lon, lat), xytext=(5, 5), 
                    textcoords='offset points', fontsize=8,
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.7))
    
    # Plot 2: Power values over packet index
    packet_indices = list(range(len(powers)))
    ax2.plot(packet_indices, powers, 'b-o', markersize=4, linewidth=1)
    ax2.set_xlabel('Packet Index')
    ax2.set_ylabel('Power (dBm)')
    ax2.set_title('Power Values Over Time')
    ax2.grid(True, alpha=0.3)
    
    # Add statistics
    avg_power = np.mean(powers)
    min_power = np.min(powers)
    max_power = np.max(powers)
    
    stats_text = f'Statistics:\nAvg: {avg_power:.2f} dBm\nMin: {min_power:.2f} dBm\nMax: {max_power:.2f} dBm'
    ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('gps_power_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Enhanced visualization saved as: gps_power_analysis.png")


def main():
    """Main function"""
    print("GPS Power Map Visualization")
    print("=" * 40)
    
    # File path - modify as needed
    file_path = "./data/0053_20250805_172705.part1.spectrum"
    
    # Check if file exists
    if not os.path.exists(file_path):
        print(f"Error: File not found: {file_path}")
        print("Please make sure the spectrum file exists in the data directory.")
        return
    
    # Extract GPS and power data
    print("Extracting GPS and power data from spectrum file...")
    gps_power_data = extract_gps_and_power_for_map(file_path)
    
    if not gps_power_data:
        print("No valid GPS and power data found in the file.")
        return
    
    print(f"Found {len(gps_power_data)} data points")
    
    # Display first few data points
    print("\nFirst few data points:")
    for i, (lat, lon, power) in enumerate(gps_power_data[:5]):
        print(f"Packet {i:2d}: GPS=({lat:10.6f}, {lon:10.6f}), Power={power:8.2f} dBm")
    
    # Create map visualization
    print("\nCreating map visualization...")
    success = create_map_with_gps_power(gps_power_data)
    
    if success:
        print("Map visualization completed successfully!")
    else:
        print("Map creation failed, but creating local visualization...")
        create_enhanced_visualization(gps_power_data)


if __name__ == "__main__":
    main()
