#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS Power Map Visualization
Combines GPS coordinates and power values from spectrum data and displays them on a map
"""

import requests
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO
import numpy as np
from typing import List, Tuple
import os
import sys

# Import functions from existing modules
from swp_mode_playback import extract_gps_and_power_for_map
from map.WGS84_GCJ02 import wgs84_to_gcj02


def get_power_color(power_dbm: float) -> str:
    """
    Convert power value to color for visualization
    
    Args:
        power_dbm: Power value in dBm
        
    Returns:
        Color string in hex format
    """
    # Normalize power to 0-1 range (assuming -100 to -50 dBm range)
    min_power = -100.0
    max_power = -50.0
    
    # Clamp the power value
    normalized_power = max(0, min(1, (power_dbm - min_power) / (max_power - min_power)))
    
    # Color gradient from blue (low power) to red (high power)
    if normalized_power < 0.5:
        # Blue to green
        r = 0
        g = int(255 * normalized_power * 2)
        b = int(255 * (1 - normalized_power * 2))
    else:
        # Green to red
        r = int(255 * (normalized_power - 0.5) * 2)
        g = int(255 * (1 - (normalized_power - 0.5) * 2))
        b = 0
    
    return f"#{r:02x}{g:02x}{b:02x}"


def create_map_with_gps_power(gps_power_data: List[Tuple[float, float, float]], 
                              api_key: str = "fd2adfc70d86343b1181195c0fc9a0ee") -> bool:
    """
    Create a map with GPS coordinates and power values
    
    Args:
        gps_power_data: List of (latitude, longitude, power_dBm) tuples
        api_key: Amap API key
        
    Returns:
        True if successful, False otherwise
    """
    if not gps_power_data:
        print("No GPS power data available")
        return False
    
    # Calculate center coordinates
    latitudes = [data[0] for data in gps_power_data]
    longitudes = [data[1] for data in gps_power_data]
    powers = [data[2] for data in gps_power_data]
    
    center_lat = sum(latitudes) / len(latitudes)
    center_lon = sum(longitudes) / len(longitudes)
    
    print(f"Center coordinates: ({center_lat:.6f}, {center_lon:.6f})")
    print(f"Power range: {min(powers):.2f} to {max(powers):.2f} dBm")
    
    # Convert to GCJ-02 coordinate system for Amap
    center_lon_gcj, center_lat_gcj = wgs84_to_gcj02(center_lon, center_lat)
    
    # Map parameters
    width = 1920
    height = 1080
    zoom = 16
    
    # Build markers string for all GPS points
    markers_list = []
    for i, (lat, lon, power) in enumerate(gps_power_data):
        # Convert each point to GCJ-02
        lon_gcj, lat_gcj = wgs84_to_gcj02(lon, lat)
        
        # Get color based on power
        color = get_power_color(power)
        color_code = color.replace('#', '0x')
        
        # Add marker (limit to first 20 points to avoid URL length issues)
        if i < 20:
            markers_list.append(f"mid,{color_code},A:{lon_gcj},{lat_gcj}")
    
    markers_str = "|".join(markers_list)
    
    # Amap static map API URL
    url = "https://restapi.amap.com/v3/staticmap"
    
    # Request parameters
    params = {
        "location": f"{center_lon_gcj},{center_lat_gcj}",
        "zoom": zoom,
        "size": f"{width}*{height}",
        "scale": 2,
        "maptype": "satellite",
        "markers": markers_str,
        "key": api_key
    }
    
    try:
        # Send request
        print("Requesting map from Amap API...")
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            # Save the map image
            map_filename = "gps_power_map.png"
            with open(map_filename, "wb") as f:
                f.write(response.content)
            
            print(f"Map saved as: {map_filename}")
            
            # Create enhanced visualization with matplotlib
            create_enhanced_visualization(gps_power_data, map_filename)
            
            return True
        else:
            print(f"Failed to fetch map: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"Error creating map: {e}")
        return False


def create_enhanced_visualization(gps_power_data: List[Tuple[float, float, float]], 
                                map_filename: str = None):
    """
    Create enhanced visualization with power values and legend
    
    Args:
        gps_power_data: List of (latitude, longitude, power_dBm) tuples
        map_filename: Background map image filename
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # Extract data
    latitudes = [data[0] for data in gps_power_data]
    longitudes = [data[1] for data in gps_power_data]
    powers = [data[2] for data in gps_power_data]
    
    # Plot 1: GPS coordinates with power color coding
    scatter = ax1.scatter(longitudes, latitudes, c=powers, cmap='coolwarm', 
                         s=100, alpha=0.7, edgecolors='black', linewidth=1)
    ax1.set_xlabel('Longitude')
    ax1.set_ylabel('Latitude')
    ax1.set_title('GPS Coordinates with Power Values')
    ax1.grid(True, alpha=0.3)
    
    # Add colorbar
    cbar = plt.colorbar(scatter, ax=ax1)
    cbar.set_label('Power (dBm)')
    
    # Add point labels
    for i, (lat, lon, power) in enumerate(gps_power_data[:10]):  # Label first 10 points
        ax1.annotate(f'P{i}\n{power:.1f}dBm', 
                    (lon, lat), xytext=(5, 5), 
                    textcoords='offset points', fontsize=8,
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.7))
    
    # Plot 2: Power values over packet index
    packet_indices = list(range(len(powers)))
    ax2.plot(packet_indices, powers, 'b-o', markersize=4, linewidth=1)
    ax2.set_xlabel('Packet Index')
    ax2.set_ylabel('Power (dBm)')
    ax2.set_title('Power Values Over Time')
    ax2.grid(True, alpha=0.3)
    
    # Add statistics
    avg_power = np.mean(powers)
    min_power = np.min(powers)
    max_power = np.max(powers)
    
    stats_text = f'Statistics:\nAvg: {avg_power:.2f} dBm\nMin: {min_power:.2f} dBm\nMax: {max_power:.2f} dBm'
    ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('gps_power_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Enhanced visualization saved as: gps_power_analysis.png")


def main():
    """Main function"""
    print("GPS Power Map Visualization")
    print("=" * 40)
    
    # File path - modify as needed
    file_path = "./data/0053_20250805_172705.part1.spectrum"
    
    # Check if file exists
    if not os.path.exists(file_path):
        print(f"Error: File not found: {file_path}")
        print("Please make sure the spectrum file exists in the data directory.")
        return
    
    # Extract GPS and power data
    print("Extracting GPS and power data from spectrum file...")
    gps_power_data = extract_gps_and_power_for_map(file_path)
    
    if not gps_power_data:
        print("No valid GPS and power data found in the file.")
        return
    
    print(f"Found {len(gps_power_data)} data points")
    
    # Display first few data points
    print("\nFirst few data points:")
    for i, (lat, lon, power) in enumerate(gps_power_data[:5]):
        print(f"Packet {i:2d}: GPS=({lat:10.6f}, {lon:10.6f}), Power={power:8.2f} dBm")
    
    # Create map visualization
    print("\nCreating map visualization...")
    success = create_map_with_gps_power(gps_power_data)
    
    if success:
        print("Map visualization completed successfully!")
    else:
        print("Map creation failed, but creating local visualization...")
        create_enhanced_visualization(gps_power_data)


if __name__ == "__main__":
    main()
