#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Power Annotated Map - 在卫星地图上直接显示功率值
"""

import requests
import matplotlib.pyplot as plt
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from typing import List, Tuple
import os

from swp_mode_playback import extract_gps_and_power_for_map
from map.WGS84_GCJ02 import wgs84_to_gcj02


def create_power_annotated_map(gps_power_data: List[Tuple[float, float, float]]):
    """
    创建在卫星地图上直接显示功率值的地图
    """
    if not gps_power_data:
        print("No GPS data available")
        return False
    
    # 提取数据
    latitudes = [data[0] for data in gps_power_data]
    longitudes = [data[1] for data in gps_power_data]
    powers = [data[2] for data in gps_power_data]
    
    # 计算中心点
    center_lat = np.mean(latitudes)
    center_lon = np.mean(longitudes)
    
    print(f"Center GPS: ({center_lat:.6f}, {center_lon:.6f})")
    print(f"Power range: {min(powers):.2f} to {max(powers):.2f} dBm")
    
    # 转换为GCJ-02坐标
    center_lon_gcj, center_lat_gcj = wgs84_to_gcj02(center_lon, center_lat)
    
    # 步骤1: 获取基础卫星地图（不带标记）
    api_key = "fd2adfc70d86343b1181195c0fc9a0ee"
    url = "https://restapi.amap.com/v3/staticmap"
    
    # 获取无标记的基础地图
    params = {
        "location": f"{center_lon_gcj},{center_lat_gcj}",
        "zoom": 16,
        "size": "1024*768",
        "scale": 2,
        "maptype": "satellite",
        "key": api_key
    }
    
    print("获取基础卫星地图...")
    try:
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            base_map_filename = "base_satellite_map.png"
            with open(base_map_filename, "wb") as f:
                f.write(response.content)
            print(f"✅ 基础地图保存: {base_map_filename}")
            
            # 步骤2: 在地图上添加功率值标注
            annotate_power_on_map(base_map_filename, gps_power_data, center_lat, center_lon)
            
            return True
        else:
            print(f"❌ 获取地图失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False


def annotate_power_on_map(map_filename: str, gps_power_data: List[Tuple[float, float, float]], 
                         center_lat: float, center_lon: float):
    """
    在卫星地图上添加功率值标注
    """
    try:
        # 加载地图图像并转换为RGB模式
        map_img = Image.open(map_filename)
        if map_img.mode != 'RGB':
            map_img = map_img.convert('RGB')
        map_width, map_height = map_img.size

        print(f"地图尺寸: {map_width} x {map_height}")
        print(f"图像模式: {map_img.mode}")

        # 创建绘图对象
        draw = ImageDraw.Draw(map_img)
        
        # 尝试加载字体
        try:
            # Windows系统字体
            font_large = ImageFont.truetype("arial.ttf", 24)
            font_small = ImageFont.truetype("arial.ttf", 18)
        except:
            try:
                # 备用字体
                font_large = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 24)
                font_small = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 18)
            except:
                # 默认字体
                font_large = ImageFont.load_default()
                font_small = ImageFont.load_default()
        
        # 计算地图的GPS范围（简化计算）
        # 假设地图中心对应图像中心
        map_center_x = map_width // 2
        map_center_y = map_height // 2
        
        # GPS坐标到像素的转换比例（粗略估算）
        # 在zoom=16时，大约每度对应的像素数
        pixels_per_degree_lat = map_height / 0.01  # 粗略估算
        pixels_per_degree_lon = map_width / 0.01   # 粗略估算
        
        print("在地图上标注功率值...")
        
        # 为每个GPS点添加功率值标注
        for i, (lat, lon, power) in enumerate(gps_power_data[:10]):  # 只显示前10个点避免过于拥挤
            
            # 计算相对于中心的偏移
            lat_offset = lat - center_lat
            lon_offset = lon - center_lon
            
            # 转换为像素坐标
            pixel_x = map_center_x + int(lon_offset * pixels_per_degree_lon)
            pixel_y = map_center_y - int(lat_offset * pixels_per_degree_lat)  # Y轴反向
            
            # 确保坐标在图像范围内
            pixel_x = max(50, min(map_width - 50, pixel_x))
            pixel_y = max(50, min(map_height - 50, pixel_y))
            
            # 根据功率值选择颜色
            if power > -75:
                color = (255, 0, 0)      # 红色 - 高功率
                circle_color = (255, 255, 255)  # 白色圆圈
            elif power > -85:
                color = (255, 255, 0)    # 黄色 - 中等功率
                circle_color = (0, 0, 0)     # 黑色圆圈
            else:
                color = (0, 0, 255)      # 蓝色 - 低功率
                circle_color = (255, 255, 255)  # 白色圆圈
            
            # 绘制标记点
            circle_radius = 15
            draw.ellipse([pixel_x - circle_radius, pixel_y - circle_radius,
                         pixel_x + circle_radius, pixel_y + circle_radius],
                        fill=color, outline=circle_color, width=3)
            
            # 绘制包序号
            draw.text((pixel_x - 5, pixel_y - 8), str(i), fill=(255, 255, 255), font=font_small)
            
            # 绘制功率值（在点的旁边）
            power_text = f"{power:.1f}dBm"
            text_x = pixel_x + 20
            text_y = pixel_y - 10
            
            # 绘制文本背景
            text_bbox = draw.textbbox((text_x, text_y), power_text, font=font_small)
            draw.rectangle(text_bbox, fill=(0, 0, 0, 180), outline=(255, 255, 255))
            
            # 绘制功率值文本
            draw.text((text_x, text_y), power_text, fill=color, font=font_small)
            
            print(f"  标注点 {i}: ({pixel_x}, {pixel_y}) = {power:.1f}dBm")
        
        # 添加图例
        add_legend_to_map(draw, map_width, map_height, font_large, font_small)
        
        # 保存标注后的地图
        output_filename = "power_annotated_satellite_map.png"
        map_img.save(output_filename)
        print(f"✅ 功率标注地图保存: {output_filename}")
        
        # 创建对比显示
        create_comparison_display(map_filename, output_filename, gps_power_data)
        
    except Exception as e:
        print(f"❌ 标注错误: {e}")
        import traceback
        traceback.print_exc()


def add_legend_to_map(draw, map_width: int, map_height: int, font_large, font_small):
    """
    在地图上添加图例
    """
    # 图例位置（右上角）
    legend_x = map_width - 200
    legend_y = 20
    
    # 图例背景
    legend_bg = [legend_x - 10, legend_y - 10, map_width - 10, legend_y + 120]
    draw.rectangle(legend_bg, fill=(0, 0, 0, 200), outline=(255, 255, 255), width=2)
    
    # 图例标题
    draw.text((legend_x, legend_y), "功率图例", fill=(255, 255, 255), font=font_large)
    
    # 图例项目
    legend_items = [
        ("高功率 > -75dBm", (255, 0, 0)),
        ("中功率 -85~-75dBm", (255, 255, 0)),
        ("低功率 < -85dBm", (0, 0, 255))
    ]
    
    for i, (text, color) in enumerate(legend_items):
        y_pos = legend_y + 25 + i * 25
        # 绘制颜色圆圈
        draw.ellipse([legend_x, y_pos, legend_x + 15, y_pos + 15], fill=color, outline=(255, 255, 255))
        # 绘制文字
        draw.text((legend_x + 20, y_pos), text, fill=(255, 255, 255), font=font_small)


def create_comparison_display(original_map: str, annotated_map: str, gps_power_data: List[Tuple[float, float, float]]):
    """
    创建对比显示：原始地图 vs 标注地图
    """
    try:
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. 原始卫星地图
        original_img = Image.open(original_map)
        ax1.imshow(original_img)
        ax1.set_title('原始卫星地图')
        ax1.axis('off')
        
        # 2. 功率标注地图
        annotated_img = Image.open(annotated_map)
        ax2.imshow(annotated_img)
        ax2.set_title('功率标注卫星地图')
        ax2.axis('off')
        
        # 3. GPS散点图
        latitudes = [data[0] for data in gps_power_data]
        longitudes = [data[1] for data in gps_power_data]
        powers = [data[2] for data in gps_power_data]
        
        scatter = ax3.scatter(longitudes, latitudes, c=powers, cmap='coolwarm', 
                             s=100, alpha=0.8, edgecolors='black', linewidth=1)
        ax3.set_xlabel('经度')
        ax3.set_ylabel('纬度')
        ax3.set_title('GPS坐标与功率分布')
        ax3.grid(True, alpha=0.3)
        
        cbar = plt.colorbar(scatter, ax=ax3)
        cbar.set_label('功率 (dBm)')
        
        # 4. 功率数据表
        ax4.axis('off')
        
        # 创建数据表
        table_data = [['包序号', '纬度', '经度', '功率(dBm)']]
        for i in range(min(10, len(gps_power_data))):
            lat, lon, power = gps_power_data[i]
            table_data.append([f'{i}', f'{lat:.6f}', f'{lon:.6f}', f'{power:.1f}'])
        
        table = ax4.table(cellText=table_data[1:], colLabels=table_data[0],
                         cellLoc='center', loc='center',
                         colWidths=[0.15, 0.28, 0.28, 0.25])
        table.auto_set_font_size(False)
        table.set_fontsize(9)
        table.scale(1, 1.5)
        
        # 表头样式
        for i in range(len(table_data[0])):
            table[(0, i)].set_facecolor('#4CAF50')
            table[(0, i)].set_text_props(weight='bold', color='white')
        
        ax4.set_title('GPS和功率数据详情', pad=20)
        
        plt.tight_layout()
        plt.savefig('power_map_comparison.png', dpi=200, bbox_inches='tight')
        plt.show()
        
        print("✅ 对比图保存: power_map_comparison.png")
        
    except Exception as e:
        print(f"❌ 创建对比图错误: {e}")


def main():
    """主函数"""
    print("="*60)
    print("功率标注卫星地图生成器")
    print("="*60)
    
    # 提取GPS数据
    file_path = "./data/0053_20250805_172705.part1.spectrum"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件未找到: {file_path}")
        return
    
    print("提取GPS和功率数据...")
    gps_power_data = extract_gps_and_power_for_map(file_path)
    
    if not gps_power_data:
        print("❌ 未找到GPS数据")
        return
    
    print(f"✅ 找到 {len(gps_power_data)} 个数据点")
    
    # 显示样本数据
    print("\n样本数据:")
    for i in range(min(5, len(gps_power_data))):
        lat, lon, power = gps_power_data[i]
        print(f"  包 {i}: GPS=({lat:.6f}, {lon:.6f}), 功率={power:.1f} dBm")
    
    # 创建功率标注地图
    print("\n创建功率标注卫星地图...")
    success = create_power_annotated_map(gps_power_data)
    
    if success:
        print("\n✅ 功率标注地图创建成功!")
        print("生成的文件:")
        print("  - base_satellite_map.png (原始卫星地图)")
        print("  - power_annotated_satellite_map.png (功率标注地图)")
        print("  - power_map_comparison.png (对比图)")
    else:
        print("❌ 创建功率标注地图失败")


if __name__ == "__main__":
    main()
