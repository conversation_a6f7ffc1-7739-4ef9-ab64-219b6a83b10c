#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final GPS Power Demo
Demonstrates the complete GPS and Power visualization system
"""

import os
import sys
from swp_mode_playback import extract_gps_and_power_for_map


def main():
    """
    Main demonstration function showing GPS coordinates and power values
    """
    print("="*70)
    print("GPS POWER MAPPING DEMONSTRATION")
    print("="*70)
    print("This script demonstrates extracting GPS coordinates and power values")
    print("from spectrum recording files and displaying them on maps.")
    print()
    
    # File path
    file_path = "./data/0053_20250805_172705.part1.spectrum"
    
    # Check if file exists
    if not os.path.exists(file_path):
        print(f"❌ Error: File not found: {file_path}")
        print("Please make sure the spectrum file exists in the data directory.")
        return
    
    print(f"📁 Processing file: {file_path}")
    print()
    
    # Extract GPS and power data
    print("🔍 Extracting GPS and power data from spectrum file...")
    gps_power_data = extract_gps_and_power_for_map(file_path)
    
    if not gps_power_data:
        print("❌ No valid GPS and power data found in the file.")
        return
    
    print(f"✅ Successfully extracted {len(gps_power_data)} data points")
    print()
    
    # Display the data in the requested format
    print("📊 GPS COORDINATES AND POWER VALUES:")
    print("-" * 60)
    
    # Show first 10 packets as requested
    for i in range(min(10, len(gps_power_data))):
        lat, lon, power = gps_power_data[i]
        if i % 10 == 0:  # Show every 10th packet as in the original output
            print(f"Packet {i:2d}: GPS=({lat:10.6f}, {lon:10.6f}), Power={power:8.2f} dBm")
    
    # Show packets 10, 20, 30, 40 as in the original example
    key_packets = [10, 20, 30, 40]
    for packet_num in key_packets:
        if packet_num < len(gps_power_data):
            lat, lon, power = gps_power_data[packet_num]
            print(f"Packet {packet_num:2d}: GPS=({lat:10.6f}, {lon:10.6f}), Power={power:8.2f} dBm")
    
    print("-" * 60)
    print()
    
    # Statistics
    powers = [data[2] for data in gps_power_data]
    latitudes = [data[0] for data in gps_power_data]
    longitudes = [data[1] for data in gps_power_data]
    
    print("📈 STATISTICS:")
    print(f"   Total packets processed: {len(gps_power_data)}")
    print(f"   GPS location: ({latitudes[0]:.6f}, {longitudes[0]:.6f})")
    print(f"   Power range: {min(powers):.2f} to {max(powers):.2f} dBm")
    print(f"   Average power: {sum(powers)/len(powers):.2f} dBm")
    print()
    
    # Available visualizations
    print("🗺️  AVAILABLE VISUALIZATIONS:")
    print("   1. gps_power_map.png - Satellite map with GPS markers")
    print("   2. gps_power_analysis.png - Statistical analysis plots")
    print("   3. detailed_gps_power_display.png - Comprehensive analysis")
    print()
    
    # Instructions for running visualizations
    print("🚀 TO CREATE VISUALIZATIONS:")
    print("   Run: python gps_power_map.py")
    print("   Run: python detailed_gps_power_display.py")
    print()
    
    print("✅ GPS Power mapping demonstration completed successfully!")
    print("="*70)


if __name__ == "__main__":
    main()
