#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Detailed GPS Power Display
Shows detailed information about each packet with GPS coordinates and power values
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from typing import List, <PERSON>ple
import os

# Import functions from existing modules
from swp_mode_playback import extract_gps_and_power_for_map


def create_detailed_display(gps_power_data: List[Tuple[float, float, float]]):
    """
    Create detailed display showing GPS coordinates and power values for each packet
    
    Args:
        gps_power_data: List of (latitude, longitude, power_dBm) tuples
    """
    if not gps_power_data:
        print("No GPS power data available")
        return
    
    # Extract data
    latitudes = [data[0] for data in gps_power_data]
    longitudes = [data[1] for data in gps_power_data]
    powers = [data[2] for data in gps_power_data]
    packet_indices = list(range(len(powers)))
    
    # Create figure with multiple subplots
    fig = plt.figure(figsize=(20, 12))
    
    # Subplot 1: GPS coordinates scatter plot with power color coding
    ax1 = plt.subplot(2, 3, 1)
    scatter = ax1.scatter(longitudes, latitudes, c=powers, cmap='coolwarm', 
                         s=150, alpha=0.8, edgecolors='black', linewidth=1)
    ax1.set_xlabel('Longitude')
    ax1.set_ylabel('Latitude')
    ax1.set_title('GPS Coordinates with Power Values')
    ax1.grid(True, alpha=0.3)
    
    # Add colorbar
    cbar1 = plt.colorbar(scatter, ax=ax1)
    cbar1.set_label('Power (dBm)')
    
    # Subplot 2: Power values over packet index
    ax2 = plt.subplot(2, 3, 2)
    ax2.plot(packet_indices, powers, 'b-o', markersize=6, linewidth=2)
    ax2.set_xlabel('Packet Index')
    ax2.set_ylabel('Power (dBm)')
    ax2.set_title('Power Values Over Packet Index')
    ax2.grid(True, alpha=0.3)
    
    # Add statistics
    avg_power = np.mean(powers)
    min_power = np.min(powers)
    max_power = np.max(powers)
    std_power = np.std(powers)
    
    stats_text = f'Statistics:\nCount: {len(powers)}\nAvg: {avg_power:.2f} dBm\nMin: {min_power:.2f} dBm\nMax: {max_power:.2f} dBm\nStd: {std_power:.2f} dBm'
    ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # Subplot 3: Power histogram
    ax3 = plt.subplot(2, 3, 3)
    ax3.hist(powers, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    ax3.set_xlabel('Power (dBm)')
    ax3.set_ylabel('Frequency')
    ax3.set_title('Power Distribution')
    ax3.grid(True, alpha=0.3)
    ax3.axvline(avg_power, color='red', linestyle='--', linewidth=2, label=f'Mean: {avg_power:.2f}')
    ax3.legend()
    
    # Subplot 4: GPS coordinate variations
    ax4 = plt.subplot(2, 3, 4)
    lat_variation = np.array(latitudes) - np.mean(latitudes)
    lon_variation = np.array(longitudes) - np.mean(longitudes)
    ax4.plot(packet_indices, lat_variation * 1e6, 'r-o', markersize=4, label='Latitude (μ°)', linewidth=1)
    ax4.plot(packet_indices, lon_variation * 1e6, 'b-s', markersize=4, label='Longitude (μ°)', linewidth=1)
    ax4.set_xlabel('Packet Index')
    ax4.set_ylabel('Coordinate Variation (micro-degrees)')
    ax4.set_title('GPS Coordinate Variations')
    ax4.grid(True, alpha=0.3)
    ax4.legend()
    
    # Subplot 5: Power vs GPS correlation
    ax5 = plt.subplot(2, 3, 5)
    # Since GPS coordinates are nearly constant, show power vs small variations
    lat_var_mm = lat_variation * 111000 * 1000  # Convert to millimeters
    lon_var_mm = lon_variation * 111000 * 1000 * np.cos(np.radians(np.mean(latitudes)))
    
    scatter2 = ax5.scatter(lat_var_mm, lon_var_mm, c=powers, cmap='coolwarm', 
                          s=100, alpha=0.8, edgecolors='black', linewidth=1)
    ax5.set_xlabel('Latitude Variation (mm)')
    ax5.set_ylabel('Longitude Variation (mm)')
    ax5.set_title('GPS Variations with Power')
    ax5.grid(True, alpha=0.3)
    
    cbar2 = plt.colorbar(scatter2, ax=ax5)
    cbar2.set_label('Power (dBm)')
    
    # Subplot 6: Data table for first 10 packets
    ax6 = plt.subplot(2, 3, 6)
    ax6.axis('off')
    
    # Create table data
    table_data = []
    table_data.append(['Packet', 'Latitude', 'Longitude', 'Power (dBm)'])
    
    for i in range(min(15, len(gps_power_data))):  # Show first 15 packets
        lat, lon, power = gps_power_data[i]
        table_data.append([f'{i}', f'{lat:.6f}', f'{lon:.6f}', f'{power:.2f}'])
    
    # Create table
    table = ax6.table(cellText=table_data[1:], colLabels=table_data[0],
                     cellLoc='center', loc='center',
                     colWidths=[0.15, 0.25, 0.25, 0.25])
    table.auto_set_font_size(False)
    table.set_fontsize(8)
    table.scale(1, 1.5)
    
    # Style the header
    for i in range(len(table_data[0])):
        table[(0, i)].set_facecolor('#4CAF50')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    ax6.set_title('First 15 Packets Data', pad=20)
    
    plt.tight_layout()
    plt.savefig('detailed_gps_power_display.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Detailed display saved as: detailed_gps_power_display.png")


def print_packet_summary(gps_power_data: List[Tuple[float, float, float]]):
    """
    Print a summary of all packets in the format requested by the user
    
    Args:
        gps_power_data: List of (latitude, longitude, power_dBm) tuples
    """
    print("\n" + "="*60)
    print("GPS COORDINATES AND POWER VALUES SUMMARY")
    print("="*60)
    
    for i, (lat, lon, power) in enumerate(gps_power_data):
        print(f"Packet {i:2d}: GPS=({lat:10.6f}, {lon:10.6f}), Power={power:8.2f} dBm")
    
    if gps_power_data:
        powers = [data[2] for data in gps_power_data]
        latitudes = [data[0] for data in gps_power_data]
        longitudes = [data[1] for data in gps_power_data]
        
        print("\n" + "-"*60)
        print("STATISTICS:")
        print(f"Total packets: {len(gps_power_data)}")
        print(f"GPS Center: ({np.mean(latitudes):.6f}, {np.mean(longitudes):.6f})")
        print(f"Power range: {np.min(powers):.2f} to {np.max(powers):.2f} dBm")
        print(f"Average power: {np.mean(powers):.2f} dBm")
        print(f"Power std dev: {np.std(powers):.2f} dBm")
        print("-"*60)


def main():
    """Main function"""
    print("Detailed GPS Power Display")
    print("=" * 40)
    
    # File path - modify as needed
    file_path = "./data/0053_20250805_172705.part1.spectrum"
    
    # Check if file exists
    if not os.path.exists(file_path):
        print(f"Error: File not found: {file_path}")
        print("Please make sure the spectrum file exists in the data directory.")
        return
    
    # Extract GPS and power data
    print("Extracting GPS and power data from spectrum file...")
    gps_power_data = extract_gps_and_power_for_map(file_path)
    
    if not gps_power_data:
        print("No valid GPS and power data found in the file.")
        return
    
    # Print packet summary in the requested format
    print_packet_summary(gps_power_data)
    
    # Create detailed visualization
    print("\nCreating detailed visualization...")
    create_detailed_display(gps_power_data)
    
    print("Detailed analysis completed successfully!")


if __name__ == "__main__":
    main()
