#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GPS数据读取
"""

from swp_mode_playback import get_spectrum_data_for_map

def test_gps_data():
    """测试GPS数据读取"""
    file_path = "./data/0053_20250805_172705.part1.spectrum"
    
    print("正在读取GPS数据...")
    gps_coordinates, max_powers = get_spectrum_data_for_map(file_path)
    
    print(f"读取到 {len(gps_coordinates)} 个GPS坐标")
    print(f"读取到 {len(max_powers)} 个功率值")
    
    if gps_coordinates:
        print("\n前5个GPS坐标:")
        for i, (lat, lon) in enumerate(gps_coordinates[:5]):
            power = max_powers[i] if i < len(max_powers) else 0
            print(f"  {i+1}: GPS=({lat:.6f}, {lon:.6f}), Power={power:.2f} dBm")
    
    if max_powers:
        print(f"\n功率值统计:")
        print(f"  最小值: {min(max_powers):.2f} dBm")
        print(f"  最大值: {max(max_powers):.2f} dBm")
        print(f"  平均值: {sum(max_powers)/len(max_powers):.2f} dBm")

if __name__ == "__main__":
    test_gps_data()
