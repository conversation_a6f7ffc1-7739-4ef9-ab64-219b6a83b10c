#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced GPS Map with clear markers and power visualization
"""

import requests
import matplotlib.pyplot as plt
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from typing import List, <PERSON><PERSON>
import os

from swp_mode_playback import extract_gps_and_power_for_map
from map.WGS84_GCJ02 import wgs84_to_gcj02


def create_enhanced_gps_map(gps_power_data: List[Tuple[float, float, float]]):
    """
    Create enhanced GPS map with clear visualization
    """
    if not gps_power_data:
        print("No GPS data available")
        return False
    
    # Extract coordinates and power
    latitudes = [data[0] for data in gps_power_data]
    longitudes = [data[1] for data in gps_power_data]
    powers = [data[2] for data in gps_power_data]
    
    # Calculate center
    center_lat = np.mean(latitudes)
    center_lon = np.mean(longitudes)
    
    print(f"Center GPS: ({center_lat:.6f}, {center_lon:.6f})")
    print(f"Power range: {min(powers):.2f} to {max(powers):.2f} dBm")
    
    # Convert to GCJ-02
    center_lon_gcj, center_lat_gcj = wgs84_to_gcj02(center_lon, center_lat)
    
    # Create multiple maps with different zoom levels
    zoom_levels = [14, 16, 18]
    api_key = "fd2adfc70d86343b1181195c0fc9a0ee"
    
    for zoom in zoom_levels:
        print(f"\nCreating map with zoom level {zoom}...")
        
        # Create markers with different colors based on power
        markers_list = []
        
        # Add center marker (large, red)
        markers_list.append(f"large,0xFF0000,A:{center_lon_gcj},{center_lat_gcj}")
        
        # Add power-based markers
        for i, (lat, lon, power) in enumerate(gps_power_data[:5]):  # First 5 points
            lon_gcj, lat_gcj = wgs84_to_gcj02(lon, lat)
            
            # Color based on power level
            if power > -75:
                color = "0xFF0000"  # Red for high power
                size = "large"
            elif power > -85:
                color = "0xFFFF00"  # Yellow for medium power
                size = "mid"
            else:
                color = "0x0000FF"  # Blue for low power
                size = "small"
            
            markers_list.append(f"{size},{color},{i+1}:{lon_gcj},{lat_gcj}")
        
        markers_str = "|".join(markers_list)
        
        # API parameters
        params = {
            "location": f"{center_lon_gcj},{center_lat_gcj}",
            "zoom": zoom,
            "size": "1024*768",
            "scale": 2,
            "maptype": "satellite",
            "markers": markers_str,
            "key": api_key
        }
        
        # Make request
        url = "https://restapi.amap.com/v3/staticmap"
        
        try:
            response = requests.get(url, params=params, timeout=30)
            
            if response.status_code == 200:
                filename = f"enhanced_gps_map_zoom{zoom}.png"
                with open(filename, "wb") as f:
                    f.write(response.content)
                print(f"✅ Map saved: {filename}")
                
                # Create annotated version
                create_annotated_map(filename, gps_power_data, zoom)
                
            else:
                print(f"❌ Failed to get map (zoom {zoom}): {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error creating map (zoom {zoom}): {e}")
    
    return True


def create_annotated_map(map_filename: str, gps_power_data: List[Tuple[float, float, float]], zoom: int):
    """
    Create annotated version of the map with power information
    """
    try:
        # Load the map image
        map_img = Image.open(map_filename)
        
        # Create matplotlib figure
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. Original satellite map
        ax1.imshow(map_img)
        ax1.set_title(f'Satellite Map (Zoom {zoom})\nGPS Location with Power Markers')
        ax1.axis('off')
        
        # 2. GPS scatter plot with power colors
        latitudes = [data[0] for data in gps_power_data]
        longitudes = [data[1] for data in gps_power_data]
        powers = [data[2] for data in gps_power_data]
        
        scatter = ax2.scatter(longitudes, latitudes, c=powers, cmap='coolwarm', 
                             s=100, alpha=0.8, edgecolors='black', linewidth=1)
        ax2.set_xlabel('Longitude')
        ax2.set_ylabel('Latitude')
        ax2.set_title('GPS Coordinates with Power Values')
        ax2.grid(True, alpha=0.3)
        
        # Add colorbar
        cbar = plt.colorbar(scatter, ax=ax2)
        cbar.set_label('Power (dBm)')
        
        # 3. Power distribution
        ax3.hist(powers, bins=15, alpha=0.7, color='skyblue', edgecolor='black')
        ax3.set_xlabel('Power (dBm)')
        ax3.set_ylabel('Frequency')
        ax3.set_title('Power Distribution')
        ax3.grid(True, alpha=0.3)
        
        # Add statistics
        avg_power = np.mean(powers)
        ax3.axvline(avg_power, color='red', linestyle='--', linewidth=2, 
                   label=f'Mean: {avg_power:.2f} dBm')
        ax3.legend()
        
        # 4. Data table
        ax4.axis('off')
        
        # Create table data
        table_data = [['Packet', 'Latitude', 'Longitude', 'Power (dBm)']]
        for i in range(min(10, len(gps_power_data))):
            lat, lon, power = gps_power_data[i]
            table_data.append([f'{i}', f'{lat:.6f}', f'{lon:.6f}', f'{power:.2f}'])
        
        # Create table
        table = ax4.table(cellText=table_data[1:], colLabels=table_data[0],
                         cellLoc='center', loc='center',
                         colWidths=[0.15, 0.28, 0.28, 0.25])
        table.auto_set_font_size(False)
        table.set_fontsize(9)
        table.scale(1, 1.5)
        
        # Style header
        for i in range(len(table_data[0])):
            table[(0, i)].set_facecolor('#4CAF50')
            table[(0, i)].set_text_props(weight='bold', color='white')
        
        ax4.set_title('GPS and Power Data (First 10 Packets)', pad=20)
        
        plt.tight_layout()
        
        # Save annotated version
        output_filename = f"annotated_gps_map_zoom{zoom}.png"
        plt.savefig(output_filename, dpi=200, bbox_inches='tight')
        plt.show()
        
        print(f"✅ Annotated map saved: {output_filename}")
        
    except Exception as e:
        print(f"❌ Error creating annotated map: {e}")


def main():
    """Main function"""
    print("="*60)
    print("ENHANCED GPS POWER MAP")
    print("="*60)
    
    # Extract GPS data
    file_path = "./data/0053_20250805_172705.part1.spectrum"
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return
    
    print("Extracting GPS and power data...")
    gps_power_data = extract_gps_and_power_for_map(file_path)
    
    if not gps_power_data:
        print("❌ No GPS data found")
        return
    
    print(f"✅ Found {len(gps_power_data)} data points")
    
    # Show sample data
    print("\nSample data:")
    for i in range(min(5, len(gps_power_data))):
        lat, lon, power = gps_power_data[i]
        print(f"  Packet {i}: GPS=({lat:.6f}, {lon:.6f}), Power={power:.2f} dBm")
    
    # Create enhanced maps
    print("\nCreating enhanced GPS maps...")
    success = create_enhanced_gps_map(gps_power_data)
    
    if success:
        print("\n✅ Enhanced GPS maps created successfully!")
        print("Generated files:")
        print("  - enhanced_gps_map_zoom14.png")
        print("  - enhanced_gps_map_zoom16.png") 
        print("  - enhanced_gps_map_zoom18.png")
        print("  - annotated_gps_map_zoom*.png")
    else:
        print("❌ Failed to create enhanced maps")


if __name__ == "__main__":
    main()
