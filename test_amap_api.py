#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Amap API functionality
"""

import requests
from PIL import Image
import matplotlib.pyplot as plt
from map.WGS84_GCJ02 import wgs84_to_gcj02


def test_amap_api():
    """Test Amap API with simple parameters"""
    
    # GPS coordinates from our data
    gps_lat = 28.221365
    gps_lon = 112.991791
    
    print(f"Original GPS coordinates (WGS84): {gps_lat}, {gps_lon}")
    
    # Convert to GCJ-02
    gcj_lon, gcj_lat = wgs84_to_gcj02(gps_lon, gps_lat)
    print(f"Converted coordinates (GCJ02): {gcj_lat}, {gcj_lon}")
    
    # Amap API parameters
    api_key = "fd2adfc70d86343b1181195c0fc9a0ee"
    url = "https://restapi.amap.com/v3/staticmap"
    
    # Simple parameters
    params = {
        "location": f"{gcj_lon},{gcj_lat}",
        "zoom": 16,
        "size": "1024*768",
        "scale": 2,
        "maptype": "satellite",
        "markers": f"mid,0xFF0000,A:{gcj_lon},{gcj_lat}",
        "key": api_key
    }
    
    print("\nTesting Amap API...")
    print(f"Request URL: {url}")
    print("Parameters:")
    for key, value in params.items():
        print(f"  {key}: {value}")
    
    try:
        response = requests.get(url, params=params, timeout=30)
        
        print(f"\nResponse status: {response.status_code}")
        print(f"Content type: {response.headers.get('content-type', 'Unknown')}")
        print(f"Content length: {len(response.content)} bytes")
        
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '')
            
            if 'image' in content_type:
                # Save the image
                filename = "test_amap_result.png"
                with open(filename, "wb") as f:
                    f.write(response.content)
                print(f"✅ Map image saved as: {filename}")
                
                # Display the image
                try:
                    img = Image.open(filename)
                    plt.figure(figsize=(12, 8))
                    plt.imshow(img)
                    plt.title(f"Amap Satellite Map\nGPS: ({gps_lat}, {gps_lon})")
                    plt.axis('off')
                    plt.tight_layout()
                    plt.savefig("test_amap_display.png", dpi=150, bbox_inches='tight')
                    plt.show()
                    print("✅ Map displayed successfully!")
                    return True
                except Exception as e:
                    print(f"❌ Error displaying image: {e}")
                    return False
            else:
                print("❌ Response is not an image")
                print("Response content:")
                print(response.text[:500])
                return False
        else:
            print(f"❌ API request failed with status {response.status_code}")
            print("Response:")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ Error making request: {e}")
        return False


def test_different_map_types():
    """Test different map types"""
    
    gps_lat = 28.221365
    gps_lon = 112.991791
    gcj_lon, gcj_lat = wgs84_to_gcj02(gps_lon, gps_lat)
    
    api_key = "fd2adfc70d86343b1181195c0fc9a0ee"
    url = "https://restapi.amap.com/v3/staticmap"
    
    map_types = ["satellite", "roadmap", "hybrid"]
    
    for map_type in map_types:
        print(f"\nTesting map type: {map_type}")
        
        params = {
            "location": f"{gcj_lon},{gcj_lat}",
            "zoom": 16,
            "size": "800*600",
            "scale": 1,
            "maptype": map_type,
            "markers": f"mid,0xFF0000,A:{gcj_lon},{gcj_lat}",
            "key": api_key
        }
        
        try:
            response = requests.get(url, params=params, timeout=30)
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                if 'image' in content_type:
                    filename = f"test_amap_{map_type}.png"
                    with open(filename, "wb") as f:
                        f.write(response.content)
                    print(f"✅ {map_type} map saved as: {filename}")
                else:
                    print(f"❌ {map_type}: Response is not an image")
            else:
                print(f"❌ {map_type}: Failed with status {response.status_code}")
                
        except Exception as e:
            print(f"❌ {map_type}: Error - {e}")


def main():
    print("="*60)
    print("AMAP API TEST")
    print("="*60)
    
    # Test basic API functionality
    success = test_amap_api()
    
    if success:
        print("\n" + "="*60)
        print("Testing different map types...")
        test_different_map_types()
    
    print("\n" + "="*60)
    print("Test completed!")


if __name__ == "__main__":
    main()
