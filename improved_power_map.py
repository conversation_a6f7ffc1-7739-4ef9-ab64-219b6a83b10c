#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Improved Power Map - 改进的功率地图显示
在卫星地图上清晰显示功率值，即使GPS坐标非常接近
"""

import requests
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from typing import List, Tuple
import os

from swp_mode_playback import extract_gps_and_power_for_map
from map.WGS84_GCJ02 import wgs84_to_gcj02


def create_improved_power_map(gps_power_data: List[Tuple[float, float, float]]):
    """
    创建改进的功率地图，在卫星地图上清晰显示功率值
    """
    if not gps_power_data:
        print("No GPS data available")
        return False
    
    # 提取数据
    latitudes = [data[0] for data in gps_power_data]
    longitudes = [data[1] for data in gps_power_data]
    powers = [data[2] for data in gps_power_data]
    
    # 计算中心点
    center_lat = np.mean(latitudes)
    center_lon = np.mean(longitudes)
    
    print(f"Center GPS: ({center_lat:.6f}, {center_lon:.6f})")
    print(f"Power range: {min(powers):.2f} to {max(powers):.2f} dBm")
    print(f"GPS coordinate range:")
    print(f"  Lat: {min(latitudes):.6f} to {max(latitudes):.6f}")
    print(f"  Lon: {min(longitudes):.6f} to {max(longitudes):.6f}")
    
    # 转换为GCJ-02坐标
    center_lon_gcj, center_lat_gcj = wgs84_to_gcj02(center_lon, center_lat)
    
    # 获取高分辨率卫星地图
    api_key = "fd2adfc70d86343b1181195c0fc9a0ee"
    url = "https://restapi.amap.com/v3/staticmap"
    
    # 使用更高的缩放级别来显示更多细节
    params = {
        "location": f"{center_lon_gcj},{center_lat_gcj}",
        "zoom": 18,  # 更高的缩放级别
        "size": "1024*768",
        "scale": 2,
        "maptype": "satellite",
        "key": api_key
    }
    
    print("获取高分辨率卫星地图...")
    try:
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            base_map_filename = "high_res_satellite_map.png"
            with open(base_map_filename, "wb") as f:
                f.write(response.content)
            print(f"✅ 高分辨率地图保存: {base_map_filename}")
            
            # 创建功率可视化
            create_power_visualization(base_map_filename, gps_power_data)
            
            return True
        else:
            print(f"❌ 获取地图失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False


def create_power_visualization(map_filename: str, gps_power_data: List[Tuple[float, float, float]]):
    """
    创建功率可视化，使用matplotlib在地图上叠加功率信息
    """
    try:
        # 加载地图
        map_img = Image.open(map_filename)
        if map_img.mode != 'RGB':
            map_img = map_img.convert('RGB')
        
        # 创建matplotlib图形
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))
        
        # 1. 原始卫星地图
        ax1.imshow(map_img)
        ax1.set_title('High Resolution Satellite Map\nGPS Location: (28.221365°, 112.991791°)', fontsize=14)
        ax1.axis('off')
        
        # 2. 卫星地图 + 功率标注
        ax2.imshow(map_img)
        
        # 在地图中心区域创建功率显示网格
        # 由于所有GPS点都非常接近，我们在中心区域创建一个网格来显示不同的功率值
        map_height, map_width = map_img.size[1], map_img.size[0]
        center_x, center_y = map_width // 2, map_height // 2
        
        # 创建一个小的网格来显示不同的功率值
        grid_size = 80  # 网格大小
        positions = []
        
        # 为前10个数据点创建不同的位置
        for i in range(min(10, len(gps_power_data))):
            # 在中心周围创建圆形分布
            angle = (i * 2 * np.pi) / 10
            radius = 30 + (i % 3) * 20  # 不同半径的圆圈
            
            pos_x = center_x + radius * np.cos(angle)
            pos_y = center_y + radius * np.sin(angle)
            
            positions.append((pos_x, pos_y))
        
        # 绘制功率值
        powers = [data[2] for data in gps_power_data]
        for i, ((lat, lon, power), (pos_x, pos_y)) in enumerate(zip(gps_power_data[:10], positions)):
            
            # 根据功率选择颜色
            if power > -75:
                color = 'red'
                marker_color = 'white'
            elif power > -85:
                color = 'yellow'
                marker_color = 'black'
            else:
                color = 'blue'
                marker_color = 'white'
            
            # 绘制圆形标记
            circle = patches.Circle((pos_x, pos_y), 15, facecolor=color, edgecolor=marker_color, linewidth=2)
            ax2.add_patch(circle)
            
            # 添加包序号
            ax2.text(pos_x, pos_y, str(i), ha='center', va='center', 
                    color=marker_color, fontsize=10, fontweight='bold')
            
            # 添加功率值标签
            ax2.text(pos_x + 25, pos_y, f'{power:.1f}dBm', 
                    bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.8),
                    color=marker_color, fontsize=9, fontweight='bold')
        
        ax2.set_title('Satellite Map with Power Values\n(Packets 0-9 distributed around center)', fontsize=14)
        ax2.axis('off')
        
        # 3. 功率分布图
        packet_indices = list(range(len(powers)))
        bars = ax3.bar(packet_indices[:15], powers[:15], 
                      color=['red' if p > -75 else 'yellow' if p > -85 else 'blue' for p in powers[:15]],
                      alpha=0.7, edgecolor='black')
        
        ax3.set_xlabel('Packet Index')
        ax3.set_ylabel('Power (dBm)')
        ax3.set_title('Power Values by Packet (First 15 packets)')
        ax3.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, (bar, power) in enumerate(zip(bars, powers[:15])):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{power:.1f}', ha='center', va='bottom', fontsize=8)
        
        # 4. 详细数据表
        ax4.axis('off')
        
        # 创建数据表
        table_data = [['Packet', 'Latitude', 'Longitude', 'Power (dBm)', 'Color']]
        colors_map = {True: 'Red (High)', False: 'Yellow (Med)' if True else 'Blue (Low)'}
        
        for i in range(min(15, len(gps_power_data))):
            lat, lon, power = gps_power_data[i]
            if power > -75:
                color_desc = 'Red (High)'
            elif power > -85:
                color_desc = 'Yellow (Med)'
            else:
                color_desc = 'Blue (Low)'
            
            table_data.append([f'{i}', f'{lat:.6f}', f'{lon:.6f}', f'{power:.1f}', color_desc])
        
        table = ax4.table(cellText=table_data[1:], colLabels=table_data[0],
                         cellLoc='center', loc='center',
                         colWidths=[0.12, 0.25, 0.25, 0.18, 0.20])
        table.auto_set_font_size(False)
        table.set_fontsize(8)
        table.scale(1, 1.2)
        
        # 表头样式
        for i in range(len(table_data[0])):
            table[(0, i)].set_facecolor('#4CAF50')
            table[(0, i)].set_text_props(weight='bold', color='white')
        
        # 根据功率值给行着色
        for i in range(1, min(16, len(table_data))):
            power_val = float(table_data[i][3])
            if power_val > -75:
                bg_color = '#ffcccc'  # 浅红色
            elif power_val > -85:
                bg_color = '#ffffcc'  # 浅黄色
            else:
                bg_color = '#ccccff'  # 浅蓝色
            
            for j in range(len(table_data[0])):
                table[(i, j)].set_facecolor(bg_color)
        
        ax4.set_title('GPS and Power Data Details\n(Color coded by power level)', pad=20)
        
        # 添加总体统计信息
        stats_text = f"""Statistics:
Total Packets: {len(gps_power_data)}
Power Range: {min(powers):.1f} to {max(powers):.1f} dBm
Average Power: {np.mean(powers):.1f} dBm
High Power (>-75dBm): {sum(1 for p in powers if p > -75)} packets
Medium Power (-85~-75dBm): {sum(1 for p in powers if -85 <= p <= -75)} packets
Low Power (<-85dBm): {sum(1 for p in powers if p < -85)} packets"""
        
        fig.text(0.02, 0.02, stats_text, fontsize=10, 
                bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        
        plt.tight_layout()
        plt.subplots_adjust(bottom=0.15)  # 为统计信息留出空间
        
        # 保存图像
        output_filename = "improved_power_satellite_map.png"
        plt.savefig(output_filename, dpi=200, bbox_inches='tight')
        plt.show()
        
        print(f"✅ 改进的功率地图保存: {output_filename}")
        
        # 创建简化的功率标注地图
        create_simple_annotated_map(map_filename, gps_power_data)
        
    except Exception as e:
        print(f"❌ 创建可视化错误: {e}")
        import traceback
        traceback.print_exc()


def create_simple_annotated_map(map_filename: str, gps_power_data: List[Tuple[float, float, float]]):
    """
    创建简化的功率标注地图，直接在卫星图上标注
    """
    try:
        # 加载并处理地图
        map_img = Image.open(map_filename)
        if map_img.mode != 'RGB':
            map_img = map_img.convert('RGB')
        
        draw = ImageDraw.Draw(map_img)
        
        # 尝试加载字体
        try:
            font = ImageFont.truetype("arial.ttf", 16)
            font_small = ImageFont.truetype("arial.ttf", 12)
        except:
            font = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        map_width, map_height = map_img.size
        center_x, center_y = map_width // 2, map_height // 2
        
        # 在地图上添加功率信息面板
        panel_width = 300
        panel_height = 400
        panel_x = map_width - panel_width - 20
        panel_y = 20
        
        # 绘制信息面板背景
        draw.rectangle([panel_x, panel_y, panel_x + panel_width, panel_y + panel_height],
                      fill=(0, 0, 0, 200), outline=(255, 255, 255), width=2)
        
        # 添加标题
        draw.text((panel_x + 10, panel_y + 10), "Power Values (dBm)", 
                 fill=(255, 255, 255), font=font)
        
        # 添加功率数据
        y_offset = 40
        for i, (lat, lon, power) in enumerate(gps_power_data[:12]):  # 显示前12个
            if power > -75:
                color = (255, 0, 0)  # 红色
            elif power > -85:
                color = (255, 255, 0)  # 黄色
            else:
                color = (0, 0, 255)  # 蓝色
            
            text = f"Packet {i:2d}: {power:6.1f} dBm"
            draw.text((panel_x + 10, panel_y + y_offset), text, 
                     fill=color, font=font_small)
            y_offset += 25
        
        # 添加统计信息
        powers = [data[2] for data in gps_power_data]
        stats_y = panel_y + y_offset + 20
        
        stats = [
            f"Total: {len(powers)} packets",
            f"Range: {min(powers):.1f} ~ {max(powers):.1f}",
            f"Average: {np.mean(powers):.1f} dBm",
            f"Location: (28.221365, 112.991791)"
        ]
        
        for stat in stats:
            draw.text((panel_x + 10, stats_y), stat, 
                     fill=(255, 255, 255), font=font_small)
            stats_y += 20
        
        # 在中心添加位置标记
        marker_size = 20
        draw.ellipse([center_x - marker_size, center_y - marker_size,
                     center_x + marker_size, center_y + marker_size],
                    fill=(255, 0, 0), outline=(255, 255, 255), width=3)
        
        draw.text((center_x - 30, center_y + 25), "GPS Location", 
                 fill=(255, 255, 255), font=font_small)
        
        # 保存标注地图
        output_filename = "simple_power_annotated_map.png"
        map_img.save(output_filename)
        print(f"✅ 简化功率标注地图保存: {output_filename}")
        
    except Exception as e:
        print(f"❌ 创建简化标注地图错误: {e}")


def main():
    """主函数"""
    print("="*60)
    print("改进的功率地图生成器")
    print("="*60)
    
    # 提取GPS数据
    file_path = "./data/0053_20250805_172705.part1.spectrum"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件未找到: {file_path}")
        return
    
    print("提取GPS和功率数据...")
    gps_power_data = extract_gps_and_power_for_map(file_path)
    
    if not gps_power_data:
        print("❌ 未找到GPS数据")
        return
    
    print(f"✅ 找到 {len(gps_power_data)} 个数据点")
    
    # 显示样本数据
    print("\n样本数据 (前10个包):")
    for i in range(min(10, len(gps_power_data))):
        lat, lon, power = gps_power_data[i]
        print(f"  包 {i:2d}: GPS=({lat:.6f}, {lon:.6f}), 功率={power:6.1f} dBm")
    
    # 创建改进的功率地图
    print("\n创建改进的功率地图...")
    success = create_improved_power_map(gps_power_data)
    
    if success:
        print("\n✅ 改进的功率地图创建成功!")
        print("生成的文件:")
        print("  - high_res_satellite_map.png (高分辨率卫星地图)")
        print("  - improved_power_satellite_map.png (改进的功率可视化)")
        print("  - simple_power_annotated_map.png (简化功率标注地图)")
    else:
        print("❌ 创建改进功率地图失败")


if __name__ == "__main__":
    main()
