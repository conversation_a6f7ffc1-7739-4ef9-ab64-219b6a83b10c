#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Spectrum Map Display - 在地图上显示频谱文件的经纬度和最大功率值
结合swp_mode_playback.py读取的数据和map.py的地图功能
"""

import requests
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO
import numpy as np
import os
from typing import List, <PERSON><PERSON>
from swp_mode_playback import get_spectrum_data_for_map
from map.WGS84_GCJ02 import wgs84_to_gcj02


class SpectrumMapDisplay:
    """频谱地图显示类"""
    
    def __init__(self, api_key: str = "fd2adfc70d86343b1181195c0fc9a0ee"):
        """
        初始化地图显示器
        
        Args:
            api_key: 高德地图API密钥
        """
        self.api_key = api_key
        self.url = "https://restapi.amap.com/v3/staticmap"
        
    def get_map_bounds(self, gps_coordinates: List[Tuple[float, float]]) -> Tuple[float, float, float, float]:
        """
        计算GPS坐标的边界
        
        Args:
            gps_coordinates: GPS坐标列表 [(lat, lon), ...]
            
        Returns:
            (min_lat, max_lat, min_lon, max_lon)
        """
        if not gps_coordinates:
            return 0, 0, 0, 0
            
        lats = [coord[0] for coord in gps_coordinates]
        lons = [coord[1] for coord in gps_coordinates]
        
        return min(lats), max(lats), min(lons), max(lons)
    
    def calculate_center_and_zoom(self, gps_coordinates: List[Tuple[float, float]]) -> Tuple[float, float, int]:
        """
        根据GPS坐标计算地图中心点和缩放级别
        
        Args:
            gps_coordinates: GPS坐标列表
            
        Returns:
            (center_lat, center_lon, zoom_level)
        """
        if not gps_coordinates:
            return 28.229507446289062, 112.99501037597656, 15
            
        min_lat, max_lat, min_lon, max_lon = self.get_map_bounds(gps_coordinates)
        
        # 计算中心点
        center_lat = (min_lat + max_lat) / 2
        center_lon = (min_lon + max_lon) / 2
        
        # 计算跨度
        lat_span = max_lat - min_lat
        lon_span = max_lon - min_lon
        max_span = max(lat_span, lon_span)
        
        # 根据跨度确定缩放级别
        if max_span > 0.1:
            zoom = 10
        elif max_span > 0.01:
            zoom = 13
        elif max_span > 0.001:
            zoom = 16
        else:
            zoom = 18
            
        return center_lat, center_lon, zoom
    
    def create_power_colormap(self, max_powers: List[float]) -> Tuple[plt.cm.ScalarMappable, float, float]:
        """
        创建功率值的颜色映射
        
        Args:
            max_powers: 最大功率值列表
            
        Returns:
            (colormap, min_power, max_power)
        """
        if not max_powers:
            return None, 0, 0
            
        min_power = min(max_powers)
        max_power = max(max_powers)
        
        # 创建颜色映射：蓝色(低功率) -> 绿色 -> 黄色 -> 红色(高功率)
        norm = mcolors.Normalize(vmin=min_power, vmax=max_power)
        colormap = plt.cm.ScalarMappable(norm=norm, cmap='viridis')
        
        return colormap, min_power, max_power
    
    def power_to_color_hex(self, power: float, colormap: plt.cm.ScalarMappable) -> str:
        """
        将功率值转换为十六进制颜色代码
        
        Args:
            power: 功率值
            colormap: 颜色映射
            
        Returns:
            十六进制颜色代码 (如 "0xFF0000")
        """
        if colormap is None:
            return "0xFF0000"  # 默认红色
            
        rgba = colormap.to_rgba(power)
        # 转换为RGB并生成十六进制
        r = int(rgba[0] * 255)
        g = int(rgba[1] * 255)
        b = int(rgba[2] * 255)
        
        return f"0x{r:02X}{g:02X}{b:02X}"
    
    def create_markers_string(self, gps_coordinates: List[Tuple[float, float]], 
                            max_powers: List[float], colormap: plt.cm.ScalarMappable) -> str:
        """
        创建地图标记字符串
        
        Args:
            gps_coordinates: GPS坐标列表
            max_powers: 功率值列表
            colormap: 颜色映射
            
        Returns:
            标记字符串
        """
        markers = []
        
        for i, ((lat, lon), power) in enumerate(zip(gps_coordinates, max_powers)):
            # 转换为高德坐标系
            gcj_lon, gcj_lat = wgs84_to_gcj02(lon, lat)
            
            # 获取颜色
            color = self.power_to_color_hex(power, colormap)
            
            # 创建标记 (size,color,label:longitude,latitude)
            marker = f"small,{color},:{gcj_lon},{gcj_lat}"
            markers.append(marker)
            
            # 限制标记数量以避免URL过长
            if len(markers) >= 50:
                break
                
        return "|".join(markers)
    
    def display_spectrum_on_map(self, file_path: str, output_file: str = "spectrum_map.png") -> bool:
        """
        在地图上显示频谱数据
        
        Args:
            file_path: 频谱文件路径
            output_file: 输出图片文件名
            
        Returns:
            是否成功
        """
        print("正在读取频谱文件...")
        
        # 从频谱文件读取GPS坐标和功率值
        gps_coordinates, max_powers = get_spectrum_data_for_map(file_path)
        
        if not gps_coordinates or not max_powers:
            print("未找到有效的GPS坐标或功率数据")
            return False
            
        print(f"找到 {len(gps_coordinates)} 个有效数据点")
        
        # 计算地图中心和缩放级别
        center_lat, center_lon, zoom = self.calculate_center_and_zoom(gps_coordinates)
        print(f"地图中心: ({center_lat:.6f}, {center_lon:.6f}), 缩放级别: {zoom}")
        
        # 转换中心点坐标为高德坐标系
        gcj_center_lon, gcj_center_lat = wgs84_to_gcj02(center_lon, center_lat)
        
        # 创建功率颜色映射
        colormap, min_power, max_power = self.create_power_colormap(max_powers)
        print(f"功率范围: {min_power:.2f} 到 {max_power:.2f} dBm")
        
        # 创建标记字符串
        markers_str = self.create_markers_string(gps_coordinates, max_powers, colormap)
        
        # 图片尺寸
        width = 1920
        height = 1080
        
        # 构建请求参数
        params = {
            "location": f"{gcj_center_lon},{gcj_center_lat}",
            "zoom": zoom,
            "size": f"{width}*{height}",
            "scale": 2,  # 高清图片
            "maptype": "satellite",  # 卫星地图
            "markers": markers_str,
            "key": self.api_key
        }
        
        print("正在请求地图...")
        
        # 发送请求获取图片
        try:
            response = requests.get(self.url, params=params, timeout=30)
            
            if response.status_code == 200:
                # 保存图片
                with open(output_file, "wb") as f:
                    f.write(response.content)
                
                print(f"地图已保存到: {output_file}")
                
                # 显示图片
                image = Image.open(BytesIO(response.content))
                image.show()
                
                # 创建颜色条图例
                self.create_colorbar_legend(colormap, min_power, max_power, 
                                          output_file.replace('.png', '_colorbar.png'))
                
                return True
            else:
                print(f"获取地图失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"请求地图时出错: {e}")
            return False
    
    def create_colorbar_legend(self, colormap: plt.cm.ScalarMappable, 
                             min_power: float, max_power: float, 
                             output_file: str = "colorbar_legend.png"):
        """
        创建颜色条图例
        
        Args:
            colormap: 颜色映射
            min_power: 最小功率值
            max_power: 最大功率值
            output_file: 输出文件名
        """
        try:
            fig, ax = plt.subplots(figsize=(8, 2))
            
            # 创建颜色条
            cbar = plt.colorbar(colormap, ax=ax, orientation='horizontal')
            cbar.set_label('最大功率 (dBm)', fontsize=12)
            
            # 设置标题
            ax.set_title(f'功率范围: {min_power:.2f} 到 {max_power:.2f} dBm', fontsize=14)
            ax.axis('off')
            
            plt.tight_layout()
            plt.savefig(output_file, dpi=150, bbox_inches='tight')
            plt.close()
            
            print(f"颜色条图例已保存到: {output_file}")
            
        except Exception as e:
            print(f"创建颜色条图例时出错: {e}")


def main():
    """主函数"""
    print("频谱地图显示工具")
    print("=" * 50)
    
    # 频谱文件路径
    spectrum_file = "./data/0053_20250805_172705.part1.spectrum"
    
    # 检查文件是否存在
    if not os.path.exists(spectrum_file):
        print(f"频谱文件不存在: {spectrum_file}")
        return
    
    # 创建地图显示器
    map_display = SpectrumMapDisplay()
    
    # 在地图上显示频谱数据
    success = map_display.display_spectrum_on_map(spectrum_file, "spectrum_power_map.png")
    
    if success:
        print("频谱地图显示完成！")
    else:
        print("频谱地图显示失败！")


if __name__ == "__main__":
    main()
