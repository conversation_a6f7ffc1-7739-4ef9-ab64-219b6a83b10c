# GPS Power Mapping System

This project provides a comprehensive Python implementation for parsing spectrum recording files and visualizing GPS coordinates with power values on maps.

## 🎯 Project Overview

The system extracts GPS coordinates and power measurements from spectrum recording files and creates various visualizations including satellite maps, statistical analysis, and detailed data displays.

## 📁 Files Structure

### Core Files
- `swp_mode_playback.py` - Main spectrum file parser with GPS/Power extraction
- `gps_power_map.py` - Creates satellite maps with GPS markers and power visualization
- `detailed_gps_power_display.py` - Comprehensive analysis with multiple plots
- `final_gps_power_demo.py` - Demonstration script showing all features

### Map Integration
- `map/map.py` - Basic map display using Amap API
- `map/WGS84_GCJ02.py` - Coordinate system conversion (WGS84 to GCJ02)

### Reference Files
- `SWPMode_PlayBack.cpp` - Original C++ reference implementation
- `requirements.txt` - Python dependencies

## 🚀 Quick Start

### 1. Extract GPS and Power Data
```bash
python final_gps_power_demo.py
```

### 2. Create Map Visualization
```bash
python gps_power_map.py
```

### 3. Generate Detailed Analysis
```bash
python detailed_gps_power_display.py
```

## 📊 Sample Output

The system successfully extracts GPS coordinates and power values:

```
Packet  0: GPS=( 28.221365, 112.991791), Power=  -77.60 dBm
Packet 10: GPS=( 28.221365, 112.991791), Power=  -84.31 dBm
Packet 20: GPS=( 28.221365, 112.991791), Power=  -86.22 dBm
Packet 30: GPS=( 28.221365, 112.991791), Power=  -74.07 dBm
Packet 40: GPS=( 28.221365, 112.991791), Power=  -87.77 dBm
```

## 🗺️ Generated Visualizations

1. **gps_power_map.png** - Satellite map with GPS markers colored by power values
2. **gps_power_analysis.png** - Statistical analysis with scatter plots and power distribution
3. **detailed_gps_power_display.png** - Comprehensive 6-panel analysis including:
   - GPS coordinates with power color coding
   - Power values over time
   - Power distribution histogram
   - GPS coordinate variations
   - Correlation analysis
   - Data table

## 🔧 Features

- **GPS Coordinate Extraction**: Accurately extracts latitude/longitude from spectrum files
- **Power Measurement Analysis**: Retrieves and analyzes power values in dBm
- **Map Integration**: Uses Amap API for satellite map visualization
- **Coordinate Conversion**: Converts WGS84 to GCJ02 for Chinese mapping services
- **Statistical Analysis**: Comprehensive statistical analysis of power data
- **Multiple Visualizations**: Various plot types for different analysis needs
- **Byte Order Handling**: Proper handling of big-endian/little-endian data formats

## 📋 Requirements

- Python 3.6+
- matplotlib
- numpy
- requests
- PIL (Pillow)
- msgpack

## 📦 Installation

```bash
pip install -r requirements.txt
```

## 🎯 Usage Examples

### Basic GPS and Power Extraction
```python
from swp_mode_playback import extract_gps_and_power_for_map

# Extract data from spectrum file
file_path = "./data/0053_20250805_172705.part1.spectrum"
gps_power_data = extract_gps_and_power_for_map(file_path)

# Display results
for i, (lat, lon, power) in enumerate(gps_power_data[:5]):
    print(f"Packet {i}: GPS=({lat:.6f}, {lon:.6f}), Power={power:.2f} dBm")
```

### Create Map Visualization
```python
from gps_power_map import create_map_with_gps_power

# Create map with GPS markers
success = create_map_with_gps_power(gps_power_data)
if success:
    print("Map created successfully!")
```

## 📈 Data Format

The system processes spectrum recording files (.spectrum format) and extracts:
- **GPS Coordinates**: Latitude and longitude in WGS84 format
- **Power Values**: Signal power measurements in dBm
- **Packet Information**: Sequential packet numbering and timing

## 🌍 Coordinate Systems

- **Input**: WGS84 (World Geodetic System 1984)
- **Output for Maps**: GCJ02 (Chinese coordinate system for Amap)
- **Conversion**: Automatic conversion using built-in algorithms

## 📊 Statistical Analysis

The system provides comprehensive statistical analysis including:
- Power distribution histograms
- GPS coordinate variations
- Correlation analysis between position and power
- Time-series analysis of power measurements

## 🗂️ Output Files

- `gps_power_map.png` - Satellite map visualization
- `gps_power_analysis.png` - Statistical analysis plots
- `detailed_gps_power_display.png` - Comprehensive analysis dashboard
- `./data/MaxPower_GPS_Precise.txt` - Raw extracted data in text format

## 🔍 Technical Details

### File Format Support
- Spectrum recording files (.spectrum)
- Version 55 format validation
- Big-endian data handling
- Msgpack configuration parsing

### GPS Data Extraction
- Fixed packet size: 1976 bytes
- GPS offset: 1968 bytes within packet
- Power offset: 1936 bytes within packet (32 bytes before GPS)
- Big-endian float format

### Map Integration
- Amap (高德地图) API integration
- Satellite imagery background
- Color-coded power markers
- Automatic zoom and centering

## 🚨 Troubleshooting

### Common Issues

1. **File Not Found**
   ```
   Error: File not found: ./data/filename.spectrum
   ```
   Solution: Ensure the spectrum file exists in the data directory

2. **Invalid File Version**
   ```
   The file is not version 55 (found version XX)
   ```
   Solution: Use only version 55 spectrum files

3. **No GPS Data Found**
   ```
   No valid GPS and power data found in the file
   ```
   Solution: Check if the file contains GPS information

4. **Map API Issues**
   ```
   Failed to fetch map: HTTP 403
   ```
   Solution: Verify Amap API key is valid and has sufficient quota

## 📝 License

This project is provided as-is for educational and research purposes.

## 🤝 Contributing

Feel free to submit issues and enhancement requests!

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

1. Place your spectrum recording file in the `./data/` directory
2. Update the file path in the `swp_mode_playback()` function if needed:
```python
file_path = "./data/your_file_name.part1.spectrum"
```
3. Run the program:
```bash
python swp_mode_playback.py
```

## Output

The program will create:
- `./data/SWPMode_Data.txt` - Contains frequency and power data in tab-separated format

## File Structure

- `swp_mode_playback.py` - Main Python implementation
- `requirements.txt` - Python dependencies
- `README.md` - This documentation file

## Data Structures

### SWP_Profile_TypeDef
Contains configuration parameters like:
- Start/Stop/Center frequencies
- Reference level, RBW, VBW
- Sweep time and trace parameters
- Various mode settings

### SWP_TraceInfo_TypeDef
Contains trace-specific information:
- Trace points and sweep parameters
- Frequency and bandwidth settings
- Data format information

### MeasAuxInfo_TypeDef
Contains measurement auxiliary data:
- Maximum power and index
- Temperature and state information
- Timestamps and GPS coordinates

## Key Functions

1. `swp_recording_configuration_info()` - Reads configuration and trace info from file
2. `swp_recording_power_spectral_density_info()` - Extracts frequency/power data
3. `swp_mode_playback()` - Main processing function

## Differences from C++ Version

- Uses Python's `struct` module for binary data handling
- Implements msgpack unpacking with Python's msgpack library
- Uses Python lists instead of C++ vectors
- Simplified error handling with exceptions
- More Pythonic code structure with dataclasses

## Error Codes

- 0: Success
- -1000: File opening failed
- -1001: Configuration reading failed
- -1002: Incorrect file version (not version 55)

## Notes

- The program expects spectrum files in version 55 format
- All byte order conversions are handled automatically
- The data directory will be created if it doesn't exist
